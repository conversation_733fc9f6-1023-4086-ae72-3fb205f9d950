// 基础人类类
class Human {
    constructor(x, y, gender = null) {
        // 基础属性
        this.health = random(1, 100);
        this.intelligence = random(50, 150);
        this.stamina = random(1, 100);
        this.age = random(0, 120);
        this.hunger = random(0, 100);
        this.gender = gender || (random() > 0.5 ? 'male' : 'female');
        this.isPregnant = false;
        this.pregnancyWeeks = 0;
        
        // 位置和视觉属性
        this.x = x || random(50, width - 50);
        this.y = y || random(50, height - 50);
        this.size = 20;
        this.vx = random(-1, 1);
        this.vy = random(-1, 1);
        this.speed = 1;
        
        // 生存状态
        this.isAlive = true;
        this.lastReproduction = 0;
        this.reproductionCooldown = 0;
        
        // 动画相关
        this.animationPhase = 0;
        this.isReproducing = false;
        this.reproductionTimer = 0;
        this.birthTimer = 0;
        this.isBirthing = false;
        
        // 唯一标识
        this.id = Math.random().toString(36).substr(2, 9);
        
        // 每日消耗
        this.dailyFoodConsumption = 1;
        this.dailyWaterConsumption = 1;
    }
    
    // 更新个体状态
    update() {
        if (!this.isAlive) return;
        
        // 年龄增长 (模拟时间流逝)
        this.age += 0.001 * timeSpeed;
        
        // 饥饿度增长
        this.hunger += 0.1 * timeSpeed;
        if (this.hunger > 100) this.hunger = 100;
        
        // 体力消耗
        this.stamina -= 0.05 * timeSpeed;
        if (this.stamina < 0) this.stamina = 0;
        
        // 健康值变化
        if (this.hunger > 80) {
            this.health -= 0.2 * timeSpeed;
        } else if (this.hunger < 30 && this.health < 100) {
            this.health += 0.1 * timeSpeed;
        }
        
        // 繁殖冷却时间减少
        if (this.reproductionCooldown > 0) {
            this.reproductionCooldown -= timeSpeed;
        }
        
        // 怀孕处理
        if (this.isPregnant) {
            this.updatePregnancy();
        }
        
        // 繁殖动画处理
        if (this.isReproducing) {
            this.reproductionTimer -= timeSpeed;
            if (this.reproductionTimer <= 0) {
                this.isReproducing = false;
            }
        }
        
        // 分娩动画处理
        if (this.isBirthing) {
            this.birthTimer -= timeSpeed;
            if (this.birthTimer <= 0) {
                this.isBirthing = false;
            }
        }
        
        // 移动
        this.move();
        
        // 死亡检查
        this.checkDeath();
        
        // 动画相位更新
        this.animationPhase += 0.1;
    }
    
    // 更新怀孕状态
    updatePregnancy() {
        this.pregnancyWeeks += 0.01 * timeSpeed; // 约9个月怀孕期
        
        // 怀孕期间额外消耗
        this.dailyFoodConsumption = 2;
        this.dailyWaterConsumption = 1.5;
        
        // 怀孕完成
        if (this.pregnancyWeeks >= 9) {
            this.giveBirth();
        }
    }
    
    // 分娩
    giveBirth() {
        this.isPregnant = false;
        this.pregnancyWeeks = 0;
        this.dailyFoodConsumption = 1;
        this.dailyWaterConsumption = 1;
        this.isBirthing = true;
        this.birthTimer = 300; // 10秒分娩动画
        
        // 生成新生儿
        const babyGender = random() > 0.5 ? 'male' : 'female';
        let baby;
        
        if (babyGender === 'male') {
            baby = new Male(this.x + random(-30, 30), this.y + random(-30, 30));
        } else {
            baby = new Female(this.x + random(-30, 30), this.y + random(-30, 30));
        }
        
        // 新生儿属性设置
        baby.age = 0;
        baby.health = random(80, 100);
        baby.stamina = random(80, 100);
        baby.hunger = random(0, 20);
        baby.size = 10; // 新生儿较小
        
        simulation.addIndividual(baby);
        
        // 母亲消耗
        this.health -= 20;
        this.stamina -= 30;
        this.hunger += 30;
    }
    
    // 移动
    move() {
        // 随机移动
        this.vx += random(-0.1, 0.1);
        this.vy += random(-0.1, 0.1);
        
        // 限制速度
        this.vx = constrain(this.vx, -this.speed, this.speed);
        this.vy = constrain(this.vy, -this.speed, this.speed);
        
        // 更新位置
        this.x += this.vx;
        this.y += this.vy;
        
        // 边界检查
        if (this.x < this.size/2 || this.x > width - this.size/2) {
            this.vx *= -1;
        }
        if (this.y < this.size/2 || this.y > height - this.size/2) {
            this.vy *= -1;
        }
        
        this.x = constrain(this.x, this.size/2, width - this.size/2);
        this.y = constrain(this.y, this.size/2, height - this.size/2);
    }
    
    // 死亡检查
    checkDeath() {
        if (this.health <= 0 || this.age > 120) {
            this.isAlive = false;
        }
    }
    
    // 消耗资源
    consumeResources() {
        if (simulation.resources.food >= this.dailyFoodConsumption) {
            simulation.resources.food -= this.dailyFoodConsumption;
            this.health += 10;
            this.hunger -= 20;
        }
        
        if (simulation.resources.water >= this.dailyWaterConsumption) {
            simulation.resources.water -= this.dailyWaterConsumption;
            this.health += 5;
        }
        
        // 限制属性范围
        this.health = constrain(this.health, 0, 100);
        this.hunger = constrain(this.hunger, 0, 100);
        this.stamina = constrain(this.stamina, 0, 100);
    }
    
    // 渲染
    draw() {
        if (!this.isAlive) return;
        
        push();
        translate(this.x, this.y);
        
        // 根据性别设置颜色
        if (this.gender === 'male') {
            fill(100, 150, 255);
        } else {
            fill(255, 140, 200);
        }
        
        // 怀孕状态显示
        if (this.isPregnant) {
            // 腹部隆起动画
            const bellySize = map(this.pregnancyWeeks, 0, 9, 0, 15);
            fill(255, 180, 180);
            ellipse(0, 5, this.size + bellySize, this.size + bellySize * 0.8);
        }
        
        // 繁殖动画
        if (this.isReproducing) {
            stroke(255, 0, 0);
            strokeWeight(3);
            noFill();
            ellipse(0, 0, this.size + sin(this.animationPhase * 5) * 10);
        }
        
        // 分娩动画
        if (this.isBirthing) {
            stroke(255, 255, 0);
            strokeWeight(2);
            noFill();
            for (let i = 0; i < 3; i++) {
                ellipse(0, 0, this.size + i * 10 + sin(this.animationPhase * 10) * 5);
            }
        }
        
        // 主体
        stroke(0);
        strokeWeight(1);
        if (this.gender === 'male') {
            fill(100, 150, 255);
        } else {
            fill(255, 140, 200);
        }
        ellipse(0, 0, this.size, this.size);
        
        // 健康状态指示
        const healthColor = lerpColor(color(255, 0, 0), color(0, 255, 0), this.health / 100);
        fill(healthColor);
        ellipse(0, -this.size/2 - 5, 8, 8);
        
        // 年龄显示
        fill(0);
        textAlign(CENTER, CENTER);
        textSize(8);
        text(Math.floor(this.age), 0, 0);
        
        pop();
    }
    
    // 获取详细信息
    getInfo() {
        return {
            id: this.id,
            gender: this.gender,
            age: Math.floor(this.age),
            health: Math.floor(this.health),
            intelligence: Math.floor(this.intelligence),
            stamina: Math.floor(this.stamina),
            hunger: Math.floor(this.hunger),
            isPregnant: this.isPregnant,
            pregnancyWeeks: Math.floor(this.pregnancyWeeks),
            isAlive: this.isAlive
        };
    }
    
    // 检查是否被点击
    isClicked(mouseX, mouseY) {
        const distance = dist(mouseX, mouseY, this.x, this.y);
        return distance < this.size / 2;
    }
}
