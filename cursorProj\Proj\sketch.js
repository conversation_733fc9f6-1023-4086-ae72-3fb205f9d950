// p5.js 主脚本
function setup() {
    // 创建画布，适应容器大小
    const container = document.getElementById('canvasContainer');
    const canvas = createCanvas(container.offsetWidth, container.offsetHeight);
    canvas.parent('canvasContainer');
    
    // 初始化模拟
    simulation = new Simulation();
    
    // 设置事件监听器
    setupEventListeners();
    
    // 初始UI更新
    simulation.updateUI();
}

function draw() {
    // 背景
    background(50, 70, 90);

    if (!isPaused) {
        // 更新模拟
        simulation.update();

        // 更新UI
        simulation.updateUI();
    }

    // 绘制模拟
    simulation.draw();

    // 绘制暂停指示
    if (isPaused) {
        drawPauseIndicator();
    }

    // 绘制FPS
    drawFPS();

    // 性能检查
    checkPerformance();
}

// 绘制暂停指示器
function drawPauseIndicator() {
    fill(255, 255, 255, 150);
    rect(width/2 - 50, height/2 - 20, 100, 40, 5);

    fill(0);
    textAlign(CENTER, CENTER);
    textSize(16);
    text('已暂停', width/2, height/2);
}

// 绘制FPS
function drawFPS() {
    fill(255, 255, 255, 200);
    rect(10, height - 30, 80, 20, 3);

    fill(0);
    textAlign(LEFT, CENTER);
    textSize(12);
    text(`FPS: ${Math.floor(frameRate())}`, 15, height - 20);
}

// 鼠标点击事件
function mousePressed() {
    // 检查是否在画布内
    if (mouseX >= 0 && mouseX <= width && mouseY >= 0 && mouseY <= height) {
        simulation.handleMouseClick(mouseX, mouseY);
    }
}

// 窗口大小改变时调整画布
function windowResized() {
    const container = document.getElementById('canvasContainer');
    resizeCanvas(container.offsetWidth, container.offsetHeight);
}

// 设置事件监听器
function setupEventListeners() {
    // 暂停/继续按钮
    const pauseBtn = document.getElementById('pauseBtn');
    pauseBtn.addEventListener('click', () => {
        isPaused = !isPaused;
        pauseBtn.textContent = isPaused ? '继续' : '暂停';
    });
    
    // 重置按钮
    const resetBtn = document.getElementById('resetBtn');
    resetBtn.addEventListener('click', () => {
        simulation.reset();
        isPaused = false;
        pauseBtn.textContent = '暂停';
    });
    
    // 时间流速滑块
    const speedSlider = document.getElementById('speedSlider');
    const speedValue = document.getElementById('speedValue');
    
    speedSlider.addEventListener('input', (e) => {
        timeSpeed = parseInt(e.target.value);
        speedValue.textContent = `${timeSpeed}x`;
    });
    
    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case ' ': // 空格键暂停/继续
                e.preventDefault();
                pauseBtn.click();
                break;
            case 'r': // R键重置
            case 'R':
                resetBtn.click();
                break;
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
                const speed = parseInt(e.key);
                if (speed <= 10) {
                    speedSlider.value = speed;
                    timeSpeed = speed;
                    speedValue.textContent = `${speed}x`;
                }
                break;
        }
    });
    
    // 响应式设计
    window.addEventListener('resize', () => {
        setTimeout(windowResized, 100);
    });
}

// 添加资源的全局函数
function addResource(type, amount) {
    simulation.addResource(type, amount);
    simulation.updateUI();
}

// 工具函数：限制数值范围
function constrain(value, min, max) {
    return Math.min(Math.max(value, min), max);
}

// 工具函数：映射数值范围
function map(value, start1, stop1, start2, stop2) {
    return start2 + (stop2 - start2) * ((value - start1) / (stop1 - start1));
}

// 工具函数：线性插值颜色
function lerpColor(c1, c2, amt) {
    const r1 = red(c1);
    const g1 = green(c1);
    const b1 = blue(c1);
    const r2 = red(c2);
    const g2 = green(c2);
    const b2 = blue(c2);
    
    const r = r1 + (r2 - r1) * amt;
    const g = g1 + (g2 - g1) * amt;
    const b = b1 + (b2 - b1) * amt;
    
    return color(r, g, b);
}

// 错误处理
window.addEventListener('error', (e) => {
    console.error('发生错误:', e.error);
    
    // 显示错误信息给用户
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 10px;
        border-radius: 5px;
        z-index: 1000;
        max-width: 300px;
    `;
    errorDiv.textContent = `错误: ${e.error.message}`;
    document.body.appendChild(errorDiv);
    
    // 5秒后自动移除错误提示
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
});

// 性能监控
let lastFrameTime = 0;
let frameTimeHistory = [];

function monitorPerformance() {
    const currentTime = millis();
    const frameTime = currentTime - lastFrameTime;
    lastFrameTime = currentTime;
    
    frameTimeHistory.push(frameTime);
    if (frameTimeHistory.length > 60) {
        frameTimeHistory.shift();
    }
    
    // 如果帧率过低，自动降低时间流速
    const averageFrameTime = frameTimeHistory.reduce((a, b) => a + b, 0) / frameTimeHistory.length;
    if (averageFrameTime > 33 && timeSpeed > 1) { // 低于30fps
        timeSpeed = Math.max(1, timeSpeed - 1);
        document.getElementById('speedSlider').value = timeSpeed;
        document.getElementById('speedValue').textContent = `${timeSpeed}x`;
    }
}

// 在draw函数中调用性能监控（每60帧一次）
let performanceCheckCounter = 0;
function checkPerformance() {
    performanceCheckCounter++;
    if (performanceCheckCounter >= 60) {
        monitorPerformance();
        performanceCheckCounter = 0;
    }
}

// 性能检查已集成到主draw函数中
