// 模拟系统类
class Simulation {
    constructor() {
        this.individuals = [];
        this.resources = {
            food: 100,
            water: 100,
            shelter: 20
        };
        
        this.statistics = {
            totalPopulation: 0,
            maleCount: 0,
            femaleCount: 0,
            pregnantCount: 0,
            averageAge: 0,
            births: 0,
            deaths: 0
        };
        
        this.selectedIndividual = null;
        this.dayCounter = 0;
        this.resourceConsumptionTimer = 0;
        
        // 初始化种群
        this.initializePopulation();
    }
    
    // 初始化种群
    initializePopulation() {
        // 创建初始种群
        for (let i = 0; i < 10; i++) {
            const gender = random() > 0.5 ? 'male' : 'female';
            let individual;
            
            if (gender === 'male') {
                individual = new Male();
            } else {
                individual = new Female();
            }
            
            // 设置成年个体
            individual.age = random(18, 40);
            individual.health = random(70, 100);
            individual.stamina = random(70, 100);
            individual.hunger = random(0, 30);
            
            this.individuals.push(individual);
        }
    }
    
    // 更新模拟
    update() {
        // 更新所有个体
        for (let i = this.individuals.length - 1; i >= 0; i--) {
            const individual = this.individuals[i];
            individual.update();
            
            // 移除死亡个体
            if (!individual.isAlive) {
                if (individual === this.selectedIndividual) {
                    this.selectedIndividual = null;
                }
                this.individuals.splice(i, 1);
                this.statistics.deaths++;
            }
        }
        
        // 资源消耗 (每秒一次)
        this.resourceConsumptionTimer += timeSpeed;
        if (this.resourceConsumptionTimer >= 60) {
            this.consumeResources();
            this.resourceConsumptionTimer = 0;
            this.dayCounter++;
        }
        
        // 更新统计信息
        this.updateStatistics();
        
        // 资源短缺效果
        this.applyResourceEffects();
    }
    
    // 资源消耗
    consumeResources() {
        const aliveIndividuals = this.individuals.filter(i => i.isAlive);
        
        for (let individual of aliveIndividuals) {
            individual.consumeResources();
        }
        
        // 确保资源不为负数
        this.resources.food = Math.max(0, this.resources.food);
        this.resources.water = Math.max(0, this.resources.water);
        this.resources.shelter = Math.max(0, this.resources.shelter);
    }
    
    // 应用资源效果
    applyResourceEffects() {
        const aliveIndividuals = this.individuals.filter(i => i.isAlive);
        const populationCount = aliveIndividuals.length;
        
        // 食物短缺效果
        if (this.resources.food < populationCount * 0.5) {
            for (let individual of aliveIndividuals) {
                individual.health -= 5 * timeSpeed / 60;
            }
        }
        
        // 住所不足效果 - 停止繁殖
        const shelterRatio = this.resources.shelter / populationCount;
        if (shelterRatio < 0.3) {
            // 这个效果在Male类的attemptReproduction方法中处理
        }
    }
    
    // 更新统计信息
    updateStatistics() {
        const aliveIndividuals = this.individuals.filter(i => i.isAlive);
        
        this.statistics.totalPopulation = aliveIndividuals.length;
        this.statistics.maleCount = aliveIndividuals.filter(i => i.gender === 'male').length;
        this.statistics.femaleCount = aliveIndividuals.filter(i => i.gender === 'female').length;
        this.statistics.pregnantCount = aliveIndividuals.filter(i => i.isPregnant).length;
        
        // 计算平均年龄
        if (aliveIndividuals.length > 0) {
            const totalAge = aliveIndividuals.reduce((sum, i) => sum + i.age, 0);
            this.statistics.averageAge = totalAge / aliveIndividuals.length;
        } else {
            this.statistics.averageAge = 0;
        }
    }
    
    // 添加个体
    addIndividual(individual) {
        this.individuals.push(individual);
        this.statistics.births++;
    }
    
    // 添加资源
    addResource(type, amount) {
        if (this.resources.hasOwnProperty(type)) {
            this.resources[type] += amount;
        }
    }
    
    // 渲染所有个体
    draw() {
        // 绘制背景网格
        this.drawGrid();
        
        // 绘制资源指示器
        this.drawResourceIndicators();
        
        // 绘制所有个体
        for (let individual of this.individuals) {
            if (individual.isAlive) {
                individual.draw();
            }
        }
        
        // 绘制选中个体的高亮
        if (this.selectedIndividual && this.selectedIndividual.isAlive) {
            this.drawSelectionHighlight();
        }
        
        // 绘制统计信息
        this.drawStatistics();
    }
    
    // 绘制网格
    drawGrid() {
        stroke(255, 255, 255, 50);
        strokeWeight(1);
        
        // 垂直线
        for (let x = 0; x < width; x += 50) {
            line(x, 0, x, height);
        }
        
        // 水平线
        for (let y = 0; y < height; y += 50) {
            line(0, y, width, y);
        }
    }
    
    // 绘制资源指示器
    drawResourceIndicators() {
        const indicatorY = 20;
        const spacing = 150;
        
        // 食物指示器
        fill(255, 200, 100);
        ellipse(50, indicatorY, 15, 15);
        fill(255);
        textAlign(LEFT, CENTER);
        text(`食物: ${Math.floor(this.resources.food)}`, 70, indicatorY);
        
        // 水指示器
        fill(100, 200, 255);
        ellipse(50 + spacing, indicatorY, 15, 15);
        fill(255);
        text(`水: ${Math.floor(this.resources.water)}`, 70 + spacing, indicatorY);
        
        // 住所指示器
        fill(150, 100, 50);
        rect(45 + spacing * 2, indicatorY - 7, 15, 15);
        fill(255);
        text(`住所: ${Math.floor(this.resources.shelter)}`, 70 + spacing * 2, indicatorY);
    }
    
    // 绘制选中个体高亮
    drawSelectionHighlight() {
        push();
        translate(this.selectedIndividual.x, this.selectedIndividual.y);
        stroke(255, 255, 0);
        strokeWeight(3);
        noFill();
        ellipse(0, 0, this.selectedIndividual.size + 20, this.selectedIndividual.size + 20);
        pop();
    }
    
    // 绘制统计信息
    drawStatistics() {
        // 在右上角显示基本统计
        fill(255, 255, 255, 200);
        rect(width - 200, 10, 190, 100, 5);
        
        fill(0);
        textAlign(LEFT, TOP);
        textSize(12);
        text(`总人口: ${this.statistics.totalPopulation}`, width - 190, 25);
        text(`男性: ${this.statistics.maleCount}`, width - 190, 40);
        text(`女性: ${this.statistics.femaleCount}`, width - 190, 55);
        text(`怀孕: ${this.statistics.pregnantCount}`, width - 190, 70);
        text(`平均年龄: ${Math.floor(this.statistics.averageAge)}`, width - 190, 85);
    }
    
    // 处理鼠标点击
    handleMouseClick(mouseX, mouseY) {
        // 检查是否点击了个体
        for (let individual of this.individuals) {
            if (individual.isAlive && individual.isClicked(mouseX, mouseY)) {
                this.selectedIndividual = individual;
                this.updateIndividualInfo();
                return;
            }
        }
        
        // 如果没有点击个体，取消选择
        this.selectedIndividual = null;
        this.updateIndividualInfo();
    }
    
    // 更新个体信息显示
    updateIndividualInfo() {
        const infoDiv = document.getElementById('individualInfo');
        
        if (this.selectedIndividual && this.selectedIndividual.isAlive) {
            const info = this.selectedIndividual.getInfo();
            let infoHTML = `<strong>个体 ID: ${info.id}</strong><br>`;
            infoHTML += `性别: <span class="${info.gender === 'male' ? 'male-indicator' : 'female-indicator'}">${info.gender === 'male' ? '男性' : '女性'}</span><br>`;
            infoHTML += `年龄: ${info.age}<br>`;
            infoHTML += `健康: ${info.health}<br>`;
            infoHTML += `智力: ${info.intelligence}<br>`;
            infoHTML += `体力: ${info.stamina}<br>`;
            infoHTML += `饥饿度: ${info.hunger}<br>`;
            
            if (info.isPregnant) {
                infoHTML += `<span class="pregnant-indicator">怀孕中: ${info.pregnancyWeeks}/9 个月</span><br>`;
            }
            
            // 性别特有信息
            if (info.gender === 'male') {
                infoHTML += `<br><strong>男性特征:</strong><br>`;
                infoHTML += `睾酮水平: ${info.testosteroneLevel} ng/dL<br>`;
                infoHTML += `精子质量: ${info.spermQuality}<br>`;
                infoHTML += `精子数量: ${info.spermCount} 万/mL<br>`;
                infoHTML += `体毛密度: ${info.bodyHairDensity}%<br>`;
                infoHTML += `可繁殖: ${info.canReproduce ? '是' : '否'}<br>`;
                if (info.reproductionCooldown > 0) {
                    infoHTML += `繁殖冷却: ${info.reproductionCooldown}h<br>`;
                }
            } else {
                infoHTML += `<br><strong>女性特征:</strong><br>`;
                infoHTML += `雌激素水平: ${info.estrogenLevel} pg/mL<br>`;
                infoHTML += `卵子储备: ${info.eggReserve}<br>`;
                infoHTML += `月经周期: ${info.menstrualCycle}天<br>`;
                infoHTML += `周期第: ${info.cycleDay}天<br>`;
                infoHTML += `受孕概率: ${info.conceptionProbability}%<br>`;
                infoHTML += `乳房发育: ${info.breastDevelopment}级<br>`;
                infoHTML += `排卵期: ${info.isOvulating ? '是' : '否'}<br>`;
                infoHTML += `可繁殖: ${info.canReproduce ? '是' : '否'}<br>`;
                infoHTML += `怀孕次数: ${info.pregnancyCount}<br>`;
                if (info.isInMenopause) {
                    infoHTML += `<span style="color: gray;">已进入更年期</span><br>`;
                }
            }
            
            infoDiv.innerHTML = infoHTML;
        } else {
            infoDiv.innerHTML = '点击个体查看详细信息';
        }
    }
    
    // 重置模拟
    reset() {
        this.individuals = [];
        this.resources = {
            food: 100,
            water: 100,
            shelter: 20
        };
        this.statistics = {
            totalPopulation: 0,
            maleCount: 0,
            femaleCount: 0,
            pregnantCount: 0,
            averageAge: 0,
            births: 0,
            deaths: 0
        };
        this.selectedIndividual = null;
        this.dayCounter = 0;
        this.resourceConsumptionTimer = 0;
        
        this.initializePopulation();
        this.updateUI();
    }
    
    // 更新UI
    updateUI() {
        document.getElementById('totalPopulation').textContent = this.statistics.totalPopulation;
        document.getElementById('maleCount').textContent = this.statistics.maleCount;
        document.getElementById('femaleCount').textContent = this.statistics.femaleCount;
        document.getElementById('pregnantCount').textContent = this.statistics.pregnantCount;
        document.getElementById('averageAge').textContent = Math.floor(this.statistics.averageAge);
        
        document.getElementById('foodCount').textContent = Math.floor(this.resources.food);
        document.getElementById('waterCount').textContent = Math.floor(this.resources.water);
        document.getElementById('shelterCount').textContent = Math.floor(this.resources.shelter);
    }
}

// 全局变量
let simulation;
let timeSpeed = 1;
let isPaused = false;
