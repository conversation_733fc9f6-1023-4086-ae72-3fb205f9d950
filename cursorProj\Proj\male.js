// 男性类，继承自Human
class Male extends Human {
    constructor(x, y) {
        super(x, y, 'male');
        
        // 男性特有属性
        this.testosteroneLevel = random(200, 1000); // 睾酮水平 (ng/dL)
        this.spermQuality = random(0, 100); // 精子质量
        this.spermCount = random(2000, 30000); // 精子数量 (万/毫升)
        this.reproductionCooldown = 0; // 繁殖冷却时间 (小时)
        this.bodyHairDensity = random(0, 100); // 体毛密度 (%)
        
        // 男性特有的生理周期
        this.hormoneCycle = 0;
        this.maxReproductionCooldown = 72; // 最大冷却时间72小时
    }
    
    // 重写更新方法
    update() {
        super.update();
        
        if (!this.isAlive) return;
        
        // 睾酮水平随年龄变化
        if (this.age > 30) {
            this.testosteroneLevel -= 0.01 * timeSpeed;
        }
        this.testosteroneLevel = constrain(this.testosteroneLevel, 100, 1000);
        
        // 精子质量随健康状态变化
        if (this.health > 70) {
            this.spermQuality += 0.1 * timeSpeed;
        } else {
            this.spermQuality -= 0.2 * timeSpeed;
        }
        this.spermQuality = constrain(this.spermQuality, 0, 100);
        
        // 精子数量恢复
        if (this.spermCount < 30000) {
            this.spermCount += 10 * timeSpeed;
        }
        this.spermCount = constrain(this.spermCount, 0, 30000);
        
        // 荷尔蒙周期
        this.hormoneCycle += 0.1 * timeSpeed;
        
        // 寻找繁殖对象
        if (this.canReproduce()) {
            this.seekMate();
        }
    }
    
    // 检查是否可以繁殖
    canReproduce() {
        return this.isAlive && 
               this.age >= 16 && 
               this.age <= 80 && 
               this.health > 30 && 
               this.reproductionCooldown <= 0 && 
               this.spermQuality > 20 &&
               !this.isReproducing;
    }
    
    // 寻找配偶
    seekMate() {
        const nearbyFemales = simulation.individuals.filter(individual => 
            individual instanceof Female && 
            individual.canReproduce() && 
            dist(this.x, this.y, individual.x, individual.y) < 100
        );
        
        if (nearbyFemales.length > 0) {
            // 选择最近的可繁殖女性
            const closestFemale = nearbyFemales.reduce((closest, current) => {
                const closestDist = dist(this.x, this.y, closest.x, closest.y);
                const currentDist = dist(this.x, this.y, current.x, current.y);
                return currentDist < closestDist ? current : closest;
            });
            
            this.attemptReproduction(closestFemale);
        }
    }
    
    // 尝试繁殖
    attemptReproduction(female) {
        if (!female.canReproduce() || !this.canReproduce()) return;
        
        // 计算繁殖概率
        const reproductionProbability = this.calculateReproductionProbability(female);
        
        // 检查资源限制
        const populationCount = simulation.individuals.filter(i => i.isAlive).length;
        const shelterRatio = simulation.resources.shelter / populationCount;
        
        if (shelterRatio < 0.3) {
            return; // 住所不足，停止繁殖
        }
        
        if (random(100) < reproductionProbability) {
            this.reproduce(female);
        }
    }
    
    // 计算繁殖概率
    calculateReproductionProbability(female) {
        const maleAgeFactor = Math.max(0, 1 - (this.age - 25) / 50);
        const femaleAgeFactor = Math.max(0, 1 - (female.age - 25) / 40);
        
        const baseProbability = (this.spermQuality * 0.3 + female.conceptionProbability * 0.7);
        const ageFactor = maleAgeFactor * femaleAgeFactor;
        
        return baseProbability * ageFactor * 0.1; // 降低基础概率使繁殖更现实
    }
    
    // 繁殖过程
    reproduce(female) {
        // 开始繁殖动画
        this.isReproducing = true;
        this.reproductionTimer = 150; // 5秒动画
        female.isReproducing = true;
        female.reproductionTimer = 150;
        
        // 移动到女性位置附近
        this.x = female.x + random(-20, 20);
        this.y = female.y + random(-20, 20);
        
        // 消耗体力和精子
        this.stamina -= 20;
        this.spermCount -= random(1000, 5000);
        this.reproductionCooldown = this.maxReproductionCooldown;
        
        // 女性怀孕
        setTimeout(() => {
            if (female.isAlive && !female.isPregnant) {
                female.becomePregnant();
            }
        }, 5000); // 5秒后怀孕
        
        // 增加饥饿度
        this.hunger += 15;
        female.hunger += 10;
    }
    
    // 重写渲染方法
    draw() {
        if (!this.isAlive) return;
        
        push();
        translate(this.x, this.y);
        
        // 男性特有的视觉特征
        fill(100, 150, 255, 200);
        
        // 繁殖动画
        if (this.isReproducing) {
            stroke(0, 100, 255);
            strokeWeight(3);
            noFill();
            ellipse(0, 0, this.size + sin(this.animationPhase * 5) * 10);
        }
        
        // 主体
        stroke(0);
        strokeWeight(1);
        fill(100, 150, 255);
        ellipse(0, 0, this.size, this.size);
        
        // 体毛密度显示（小点）
        if (this.bodyHairDensity > 50) {
            fill(50);
            for (let i = 0; i < this.bodyHairDensity / 20; i++) {
                const angle = random(TWO_PI);
                const radius = random(this.size / 4);
                const px = cos(angle) * radius;
                const py = sin(angle) * radius;
                ellipse(px, py, 1, 1);
            }
        }
        
        // 睾酮水平指示（蓝色条）
        stroke(0, 0, 255);
        strokeWeight(2);
        const testosteroneHeight = map(this.testosteroneLevel, 200, 1000, 2, 8);
        line(-this.size/2 - 5, -testosteroneHeight/2, -this.size/2 - 5, testosteroneHeight/2);
        
        // 健康状态指示
        const healthColor = lerpColor(color(255, 0, 0), color(0, 255, 0), this.health / 100);
        fill(healthColor);
        ellipse(0, -this.size/2 - 5, 8, 8);
        
        // 年龄显示
        fill(0);
        textAlign(CENTER, CENTER);
        textSize(8);
        text(Math.floor(this.age), 0, 0);
        
        // 繁殖冷却指示
        if (this.reproductionCooldown > 0) {
            fill(255, 0, 0, 100);
            const cooldownAngle = map(this.reproductionCooldown, 0, this.maxReproductionCooldown, 0, TWO_PI);
            arc(0, 0, this.size + 5, this.size + 5, 0, cooldownAngle);
        }
        
        pop();
    }
    
    // 重写获取信息方法
    getInfo() {
        const baseInfo = super.getInfo();
        return {
            ...baseInfo,
            testosteroneLevel: Math.floor(this.testosteroneLevel),
            spermQuality: Math.floor(this.spermQuality),
            spermCount: Math.floor(this.spermCount),
            reproductionCooldown: Math.floor(this.reproductionCooldown),
            bodyHairDensity: Math.floor(this.bodyHairDensity),
            canReproduce: this.canReproduce()
        };
    }
}
