// 女性类，继承自Human
class Female extends Human {
    constructor(x, y) {
        super(x, y, 'female');
        
        // 女性特有属性
        this.estrogenLevel = random(30, 400); // 雌激素水平 (pg/mL)
        this.eggReserve = random(25000, 2000000); // 卵子储备量
        this.menstrualCycle = random(21, 35); // 月经周期 (天)
        this.conceptionProbability = random(0, 30); // 受孕概率 (%)
        this.breastDevelopment = random(1, 5); // 乳房发育程度 (1-5级)
        
        // 生理周期相关
        this.cycleDay = random(0, this.menstrualCycle);
        this.isOvulating = false;
        this.fertilityWindow = false;
        
        // 怀孕相关
        this.maxPregnancies = random(3, 8);
        this.pregnancyCount = 0;
        
        // 更年期
        this.menopauseAge = random(45, 55);
        this.isInMenopause = false;
    }
    
    // 重写更新方法
    update() {
        super.update();
        
        if (!this.isAlive) return;
        
        // 更年期检查
        if (this.age >= this.menopauseAge && !this.isInMenopause) {
            this.enterMenopause();
        }
        
        // 雌激素水平随年龄和周期变化
        if (!this.isInMenopause) {
            this.updateHormonalCycle();
        }
        
        // 卵子储备量随年龄减少
        if (this.age > 25) {
            this.eggReserve -= 0.5 * timeSpeed;
        }
        this.eggReserve = Math.max(0, this.eggReserve);
        
        // 受孕概率随健康状态变化
        if (this.health > 70 && !this.isInMenopause) {
            this.conceptionProbability += 0.05 * timeSpeed;
        } else {
            this.conceptionProbability -= 0.1 * timeSpeed;
        }
        this.conceptionProbability = constrain(this.conceptionProbability, 0, 30);
        
        // 怀孕期间的特殊更新
        if (this.isPregnant) {
            this.updatePregnancyEffects();
        }
    }
    
    // 更新荷尔蒙周期
    updateHormonalCycle() {
        this.cycleDay += 0.1 * timeSpeed;
        
        if (this.cycleDay >= this.menstrualCycle) {
            this.cycleDay = 0;
        }
        
        // 排卵期检查 (周期中期)
        const ovulationDay = this.menstrualCycle / 2;
        this.isOvulating = Math.abs(this.cycleDay - ovulationDay) < 1;
        this.fertilityWindow = Math.abs(this.cycleDay - ovulationDay) < 3;
        
        // 雌激素水平随周期变化
        if (this.isOvulating) {
            this.estrogenLevel = Math.min(400, this.estrogenLevel + 10);
        } else {
            this.estrogenLevel = Math.max(30, this.estrogenLevel - 2);
        }
    }
    
    // 进入更年期
    enterMenopause() {
        this.isInMenopause = true;
        this.estrogenLevel = random(10, 30);
        this.conceptionProbability = 0;
        this.isOvulating = false;
        this.fertilityWindow = false;
    }
    
    // 更新怀孕效果
    updatePregnancyEffects() {
        // 怀孕期间雌激素水平升高
        this.estrogenLevel = Math.min(600, this.estrogenLevel + 5);
        
        // 乳房发育
        if (this.pregnancyWeeks > 3 && this.breastDevelopment < 5) {
            this.breastDevelopment += 0.01 * timeSpeed;
        }
        
        // 体重增加（通过size表示）
        const weightGain = map(this.pregnancyWeeks, 0, 9, 0, 8);
        this.size = 20 + weightGain;
    }
    
    // 检查是否可以繁殖
    canReproduce() {
        return this.isAlive && 
               this.age >= 14 && 
               this.age <= this.menopauseAge && 
               this.health > 30 && 
               !this.isPregnant && 
               !this.isInMenopause &&
               this.eggReserve > 1000 &&
               this.pregnancyCount < this.maxPregnancies &&
               !this.isReproducing;
    }
    
    // 怀孕
    becomePregnant() {
        if (!this.canReproduce()) return false;
        
        this.isPregnant = true;
        this.pregnancyWeeks = 0;
        this.pregnancyCount++;
        this.eggReserve -= random(100, 500);
        
        // 怀孕期间停止月经周期
        this.isOvulating = false;
        this.fertilityWindow = false;
        
        return true;
    }
    
    // 重写分娩方法
    giveBirth() {
        super.giveBirth();
        
        // 分娩后恢复
        this.size = 20; // 恢复正常体重
        this.breastDevelopment = Math.min(5, this.breastDevelopment + 0.5);
        
        // 产后恢复期
        this.reproductionCooldown = random(180, 365); // 6-12个月恢复期
        
        // 重新开始月经周期
        this.cycleDay = 0;
    }
    
    // 重写渲染方法
    draw() {
        if (!this.isAlive) return;
        
        push();
        translate(this.x, this.y);
        
        // 女性特有的视觉特征
        fill(255, 140, 200, 200);
        
        // 怀孕状态显示
        if (this.isPregnant) {
            // 腹部隆起动画
            const bellySize = map(this.pregnancyWeeks, 0, 9, 0, 15);
            fill(255, 180, 180);
            ellipse(0, 5, this.size + bellySize, this.size + bellySize * 0.8);
            
            // 怀孕进度环
            stroke(255, 100, 150);
            strokeWeight(3);
            noFill();
            const progressAngle = map(this.pregnancyWeeks, 0, 9, 0, TWO_PI);
            arc(0, 0, this.size + 20, this.size + 20, -PI/2, -PI/2 + progressAngle);
        }
        
        // 繁殖动画
        if (this.isReproducing) {
            stroke(255, 100, 150);
            strokeWeight(3);
            noFill();
            ellipse(0, 0, this.size + sin(this.animationPhase * 5) * 10);
        }
        
        // 分娩动画
        if (this.isBirthing) {
            stroke(255, 255, 0);
            strokeWeight(2);
            noFill();
            for (let i = 0; i < 3; i++) {
                ellipse(0, 0, this.size + i * 10 + sin(this.animationPhase * 10) * 5);
            }
        }
        
        // 主体
        stroke(0);
        strokeWeight(1);
        fill(255, 140, 200);
        ellipse(0, 0, this.size, this.size);
        
        // 乳房发育程度显示
        if (this.breastDevelopment > 2) {
            fill(255, 160, 220);
            const breastSize = map(this.breastDevelopment, 1, 5, 2, 8);
            ellipse(-this.size/4, -this.size/4, breastSize, breastSize);
            ellipse(this.size/4, -this.size/4, breastSize, breastSize);
        }
        
        // 雌激素水平指示（粉色条）
        stroke(255, 0, 150);
        strokeWeight(2);
        const estrogenHeight = map(this.estrogenLevel, 30, 400, 2, 8);
        line(this.size/2 + 5, -estrogenHeight/2, this.size/2 + 5, estrogenHeight/2);
        
        // 排卵期指示
        if (this.isOvulating) {
            fill(255, 255, 0, 150);
            ellipse(0, 0, this.size + 10, this.size + 10);
        }
        
        // 健康状态指示
        const healthColor = lerpColor(color(255, 0, 0), color(0, 255, 0), this.health / 100);
        fill(healthColor);
        ellipse(0, -this.size/2 - 5, 8, 8);
        
        // 年龄显示
        fill(0);
        textAlign(CENTER, CENTER);
        textSize(8);
        text(Math.floor(this.age), 0, 0);
        
        // 更年期指示
        if (this.isInMenopause) {
            stroke(100);
            strokeWeight(2);
            noFill();
            ellipse(0, 0, this.size + 15, this.size + 15);
        }
        
        pop();
    }
    
    // 重写获取信息方法
    getInfo() {
        const baseInfo = super.getInfo();
        return {
            ...baseInfo,
            estrogenLevel: Math.floor(this.estrogenLevel),
            eggReserve: Math.floor(this.eggReserve),
            menstrualCycle: Math.floor(this.menstrualCycle),
            cycleDay: Math.floor(this.cycleDay),
            conceptionProbability: Math.floor(this.conceptionProbability),
            breastDevelopment: Math.floor(this.breastDevelopment * 10) / 10,
            isOvulating: this.isOvulating,
            fertilityWindow: this.fertilityWindow,
            pregnancyCount: this.pregnancyCount,
            isInMenopause: this.isInMenopause,
            canReproduce: this.canReproduce()
        };
    }
}
