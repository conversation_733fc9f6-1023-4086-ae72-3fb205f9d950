<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哺乳动物生殖生存模拟系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .main-canvas {
            flex: 1;
            position: relative;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .panel h3 {
            margin: 0 0 10px 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .stat-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .resource-controls {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 5px;
            align-items: center;
            margin: 5px 0;
        }
        
        input[type="range"] {
            width: 100%;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
        
        .individual-info {
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
        
        .pregnant-indicator {
            color: #ff6b6b;
            font-weight: bold;
        }
        
        .male-indicator {
            color: #4dabf7;
        }
        
        .female-indicator {
            color: #ff8cc8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="panel">
                <h3>控制面板</h3>
                <div class="controls">
                    <button id="pauseBtn">暂停</button>
                    <button id="resetBtn">重置</button>
                </div>
                <div class="stat-item">
                    <span>时间流速:</span>
                    <input type="range" id="speedSlider" min="1" max="10" value="1">
                    <span id="speedValue">1x</span>
                </div>
            </div>
            
            <div class="panel">
                <h3>人口统计</h3>
                <div class="stat-item">
                    <span>总人口:</span>
                    <span class="stat-value" id="totalPopulation">0</span>
                </div>
                <div class="stat-item">
                    <span>男性:</span>
                    <span class="stat-value male-indicator" id="maleCount">0</span>
                </div>
                <div class="stat-item">
                    <span>女性:</span>
                    <span class="stat-value female-indicator" id="femaleCount">0</span>
                </div>
                <div class="stat-item">
                    <span>怀孕中:</span>
                    <span class="stat-value pregnant-indicator" id="pregnantCount">0</span>
                </div>
                <div class="stat-item">
                    <span>平均年龄:</span>
                    <span class="stat-value" id="averageAge">0</span>
                </div>
            </div>
            
            <div class="panel">
                <h3>资源管理</h3>
                <div class="resource-controls">
                    <span>食物:</span>
                    <span class="stat-value" id="foodCount">100</span>
                    <button onclick="addResource('food', 10)">+10</button>
                </div>
                <div class="resource-controls">
                    <span>水:</span>
                    <span class="stat-value" id="waterCount">100</span>
                    <button onclick="addResource('water', 10)">+10</button>
                </div>
                <div class="resource-controls">
                    <span>住所:</span>
                    <span class="stat-value" id="shelterCount">20</span>
                    <button onclick="addResource('shelter', 5)">+5</button>
                </div>
            </div>
            
            <div class="panel">
                <h3>个体详情</h3>
                <div class="individual-info" id="individualInfo">
                    点击个体查看详细信息
                </div>
            </div>
        </div>
        
        <div class="main-canvas" id="canvasContainer">
            <!-- p5.js canvas will be inserted here -->
        </div>
    </div>
    
    <script src="human.js"></script>
    <script src="male.js"></script>
    <script src="female.js"></script>
    <script src="simulation.js"></script>
    <script src="sketch.js"></script>
</body>
</html>
